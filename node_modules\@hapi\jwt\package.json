{"name": "@hapi/jwt", "description": "JWT (JSON Web Token) Authentication", "version": "3.2.0", "repository": "git://github.com/hapijs/jwt", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["jwt", "authentication", "plugin", "hapi"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/b64": "^6.0.0", "@hapi/boom": "^10.0.0", "@hapi/bounce": "^3.0.0", "@hapi/bourne": "^3.0.0", "@hapi/catbox-object": "^3.0.0", "@hapi/cryptiles": "^6.0.0", "@hapi/hoek": "^10.0.0", "@hapi/wreck": "^18.0.0", "ecdsa-sig-formatter": "^1.0.0", "joi": "^17.2.1"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "^6.0.0", "@hapi/hapi": "^21.0.0", "@hapi/lab": "^25.0.1", "node-forge": "^1.0.0", "node-rsa": "^1.0.0"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -m 10000", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}