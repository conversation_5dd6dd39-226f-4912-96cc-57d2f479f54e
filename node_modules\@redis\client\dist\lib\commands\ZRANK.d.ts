import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the rank of a member in the sorted set, with scores ordered from low to high.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param member - Member to get the rank for.
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, member: RedisArgument) => void;
    readonly transformReply: () => NumberReply | NullReply;
};
export default _default;
//# sourceMappingURL=ZRANK.d.ts.map