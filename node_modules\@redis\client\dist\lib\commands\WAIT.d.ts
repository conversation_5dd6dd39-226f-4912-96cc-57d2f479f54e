import { <PERSON><PERSON>arser } from '../client/parser';
import { NumberReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the WAIT command to synchronize with replicas
     *
     * @param parser - The command parser
     * @param numberOfReplicas - Number of replicas that must acknowledge the write
     * @param timeout - Maximum time to wait in milliseconds
     * @returns The number of replicas that acknowledged the write
     * @see https://redis.io/commands/wait/
     */
    readonly parseCommand: (this: void, parser: CommandParser, numberOfReplicas: number, timeout: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=WAIT.d.ts.map