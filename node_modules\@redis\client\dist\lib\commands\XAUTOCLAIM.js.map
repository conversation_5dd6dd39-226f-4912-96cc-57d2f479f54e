{"version": 3, "file": "XAUTOCLAIM.js", "sourceRoot": "", "sources": ["../../../lib/commands/XAUTOCLAIM.ts"], "names": [], "mappings": ";;AAEA,iEAAgG;AAwBhG,kBAAe;IACb,YAAY,EAAE,KAAK;IACnB;;;;;;;;;;;;OAYG;IACH,YAAY,CACV,MAAqB,EACrB,GAAkB,EAClB,KAAoB,EACpB,QAAuB,EACvB,WAAmB,EACnB,KAAoB,EACpB,OAA2B;QAE3B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAE5D,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IACD;;;;;;;OAOG;IACH,cAAc,CAAC,KAAsC,EAAE,QAAc,EAAE,WAAyB;QAC9F,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,QAAQ,EAAG,KAAK,CAAC,CAAC,CAA6C,CAAC,GAAG,CAAC,sDAA+B,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YACjI,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;SAC1B,CAAC;IACJ,CAAC;CACyB,CAAC"}