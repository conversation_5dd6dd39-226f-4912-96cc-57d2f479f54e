import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, NumberReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Returns the number of times an item appears in a Cuckoo Filter
     * @param parser - The command parser
     * @param key - The name of the Cuckoo filter
     * @param item - The item to count occurrences of
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, item: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=COUNT.d.ts.map