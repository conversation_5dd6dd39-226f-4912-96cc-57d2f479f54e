{"version": 3, "file": "pub-sub.js", "sourceRoot": "", "sources": ["../../../lib/client/pub-sub.ts"], "names": [], "mappings": ";;;AAGa,QAAA,WAAW,GAAG;IACzB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,SAAS;CACV,CAAC;AAMX,MAAM,QAAQ,GAAG;IACf,CAAC,mBAAW,CAAC,QAAQ,CAAC,EAAE;QACtB,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QACnC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;QACvC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;KAChC;IACD,CAAC,mBAAW,CAAC,QAAQ,CAAC,EAAE;QACtB,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;QACpC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC;QACxC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;KACjC;IACD,CAAC,mBAAW,CAAC,OAAO,CAAC,EAAE;QACrB,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;QACpC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC;QACxC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;KACjC;CACF,CAAC;AAsBF,MAAa,MAAM;IACjB,MAAM,CAAC,aAAa,CAAC,KAAoB;QACvC,OAAO,CACL,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3D,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3D,QAAQ,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,KAAoB;QAC9C,OAAO,QAAQ,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,QAAgC;QACpD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,CAAC,aAAa,CAClB,SAA2B,EAC3B,aAAiB;QAEjB,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC;IAED,YAAY,GAAG,CAAC,CAAC;IAEjB,SAAS,GAAG,KAAK,CAAC;IAElB,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,SAAS,GAAoB;QACpC,CAAC,mBAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG,EAAE;QACjC,CAAC,mBAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG,EAAE;QACjC,CAAC,mBAAW,CAAC,OAAO,CAAC,EAAE,IAAI,GAAG,EAAE;KACjC,CAAC;IAEF,SAAS,CACP,IAAgB,EAChB,QAAgC,EAChC,QAA2B,EAC3B,aAAiB;QAEjB,MAAM,IAAI,GAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAC3D,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAClD,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,IAAI,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,aAAa,EAAE,CAAC;gBACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,+EAA+E;YAC/E,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;gBACpC,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAE,EAClC,aAAa,CACd,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO;YACL,IAAI;YACJ,eAAe,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;YAChC,OAAO,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;oBACpC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAClD,IAAI,CAAC,SAAS,EAAE,CAAC;wBACf,SAAS,GAAG;4BACV,aAAa,EAAE,KAAK;4BACpB,OAAO,EAAE,IAAI,GAAG,EAAE;4BAClB,OAAO,EAAE,IAAI,GAAG,EAAE;yBACnB,CAAC;wBACF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;oBAC/C,CAAC;oBAED,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YACD,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;SACsB,CAAC;IAC5B,CAAC;IAED,sBAAsB,CACpB,IAAgB,EAChB,OAAe,EACf,SAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC;YAAE,OAAO;QAEpE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO;YACL,IAAI,EAAE;gBACJ,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS;gBACxB,OAAO;aACR;YACD,eAAe,EAAE,CAAC;YAClB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE;YAClC,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;SACsB,CAAC;IAC5B,CAAC;IAED,uBAAuB,CACrB,IAAgB,EAChB,OAAe,EACf,SAA2B;QAE3B,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACzC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACzC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,mBAAmB,CAAC,IAAgB,EAAE,SAA8B;QAClE,MAAM,IAAI,GAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;QAC9D,KAAK,MAAM,CAAC,OAAO,EAAE,gBAAgB,CAAC,IAAI,SAAS,EAAE,CAAC;YACpD,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,gBAAgB,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO;YACL,IAAI;YACJ,eAAe,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;YAChC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE;YAClC,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;SACsB,CAAC;IAC5B,CAAC;IAED,WAAW,CACT,IAAgB,EAChB,QAAiC,EACjC,QAA4B,EAC5B,aAAiB;QAEjB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,mBAAmB,CAC7B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC;YAC5B,8FAA8F;YAC9F,iGAAiG;YACjG,GAAG,EACH,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,CACxB,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,mBAAmB,CAC7B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,aAAa,CAAC,EAC9C,aAAa,CAAC,MAAM,EACpB,GAAG,EAAE;gBACH,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;oBACpC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,GAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC;QAChE,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,OAAO,EACT,KAAK,CAAC;gBACR,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;oBACvB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;oBACvB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;gBACvB,CAAC;gBAED,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC5E,IAAI,WAAW,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;oBAAE,SAAS;gBACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC5B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,oCAAoC;YACpC,iDAAiD;YACjD,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;gBACpC,MAAM,CAAC,aAAa,CAClB,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,EACvB,aAAa,CACd,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrB,CAAC;YACD,OAAO;QACT,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,EACJ,IAAI,CAAC,MAAM,GAAG,CAAC,EACf,GAAG,EAAE;YACH,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;gBACpC,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI;oBAAE,SAAS;gBAEpB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACvD,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,mBAAmB,CACjB,IAA0B,EAC1B,eAAuB,EACvB,eAA2B;QAE3B,OAAO;YACL,IAAI;YACJ,eAAe;YACf,OAAO,EAAE,GAAG,EAAE;gBACZ,eAAe,EAAE,CAAC;gBAClB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;YACD,MAAM,EAAE,SAAS;SACM,CAAC;IAC5B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,SAAS,GAAG,CACf,IAAI,CAAC,SAAS,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC;YAC9C,IAAI,CAAC,YAAY,KAAK,CAAC,CACxB,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IACxB,CAAC;IAED,WAAW;QACT,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC,SAAS,CAAC,IAAI;gBAAE,SAAS;YAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE;oBACJ,QAAQ,CAAC,IAAkB,CAAC,CAAC,SAAS;oBACtC,GAAG,SAAS,CAAC,IAAI,EAAE;iBACpB;gBACD,eAAe,EAAE,SAAS,CAAC,IAAI;gBAC/B,OAAO,EAAE,QAAQ;gBACjB,MAAM,EAAE,QAAQ;aACO,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,kBAAkB,CAAC,KAAoB;QACrC,IAAI,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CACrB,mBAAW,CAAC,QAAQ,EACpB,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,CACT,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,QAAQ,CAAC,mBAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,kBAAkB,CACrB,mBAAW,CAAC,QAAQ,EACpB,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,CACT,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,QAAQ,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,kBAAkB,CACrB,mBAAW,CAAC,OAAO,EACnB,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAC,CACT,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QACpE,IAAI,CAAC,SAAS,CAAC,mBAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,kBAAkB,CAChB,IAAgB,EAChB,OAAe,EACf,OAAe,EACf,OAAgB;QAEhB,MAAM,SAAS,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,QAAQ,EAAE,EAC/C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACzC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI;YAAE,OAAO;QAEpC,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,EAC5D,aAAa,GAAG,aAAa,KAAK,sBAAsB,CAAC,CAAC;YACxD,2CAA2C;YAC3C,6CAA6C;YAC7C,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,OAAgC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAQ,CAAC,CAAC;YAC7F,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvB,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACzC,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;CACF;AArWD,wBAqWC"}