import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Initialize a Count-Min Sketch using width and depth parameters
     * @param parser - The command parser
     * @param key - The name of the sketch
     * @param width - Number of counters in each array (must be a multiple of 2)
     * @param depth - Number of counter arrays (determines accuracy of estimates)
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, width: number, depth: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=INITBYDIM.d.ts.map