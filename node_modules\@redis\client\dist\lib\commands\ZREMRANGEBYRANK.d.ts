import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Removes all elements in the sorted set with rank between start and stop.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param start - Minimum rank (starting from 0).
     * @param stop - Maximum rank.
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, start: number, stop: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZREMRANGEBYRANK.d.ts.map