import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { ArrayReply, NumberReply, RedisArgument } from '@redis/client/dist/lib/RESP/types';
import { RedisVariadicArgument } from '@redis/client/dist/lib/commands/generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Returns the count for one or more items in a Count-Min Sketch
     * @param parser - The command parser
     * @param key - The name of the sketch
     * @param items - One or more items to get counts for
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, items: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<NumberReply>;
};
export default _default;
//# sourceMappingURL=QUERY.d.ts.map