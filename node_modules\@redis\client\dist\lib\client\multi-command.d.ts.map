{"version": 3, "file": "multi-command.d.ts", "sourceRoot": "", "sources": ["../../../lib/client/multi-command.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAA0B,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AACvH,OAAO,EAAE,oBAAoB,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAA8B,WAAW,EAAE,MAAM,eAAe,CAAC;AAGlO,OAAO,EAAE,IAAI,EAAE,MAAM,kCAAkC,CAAC;AAExD,KAAK,gBAAgB,CACnB,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,OAAO,EACjB,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,2BAA2B,CAC/E;IAAC,GAAG,OAAO;IAAE,oBAAoB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,YAAY,CAAC;CAAC,EACvE,CAAC,EACD,CAAC,EACD,CAAC,EACD,IAAI,EACJ,YAAY,CACb,CAAC;AAEF,KAAK,YAAY,CACf,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC3G,CAAC;AAEF,KAAK,WAAW,CACd,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACnF;CACF,CAAC;AAEF,KAAK,aAAa,CAChB,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACnF;CACF,CAAC;AAEF,KAAK,WAAW,CACd,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC7E,CAAC;AAEF,MAAM,MAAM,2BAA2B,CACrC,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,EAC1B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B,CACF,uBAAuB,CAAC,OAAO,CAAC,GAChC,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClD,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACjD,aAAa,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACnD,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAClD,CAAC;AAEF,KAAK,YAAY,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAE/G,MAAM,CAAC,OAAO,OAAO,uBAAuB,CAAC,OAAO,GAAG,EAAE;;IAwEvD,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC9C,CAAC,SAAS,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAChD,CAAC,SAAS,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC9C,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;gBAkB7B,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,CAAC,EAAE,WAAW;IAMhG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IAMzD,MAAM,OANK,MAAM,mBAAmB,cAAc,KAAG,IAAI,CAMpC;IAErB,UAAU,CAAC,IAAI,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAE,cAAc;IAe5D,IAAI,CAAC,CAAC,SAAS,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,EAAE,cAAc,UAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAQtH,IAAI,sGAAa;IAEjB,SAAS,CAAC,cAAc,UAAQ;IAI1B,cAAc,CAAC,CAAC,SAAS,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAQ1G,mBAAmB;CAGpB"}