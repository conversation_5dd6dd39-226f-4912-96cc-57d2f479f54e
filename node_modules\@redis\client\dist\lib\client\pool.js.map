{"version": 3, "file": "pool.js", "sourceRoot": "", "sources": ["../../../lib/client/pool.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAAmC;AAEnC,yCAA4F;AAC5F,6CAA2C;AAC3C,+CAAqF;AACrF,sCAAyC;AACzC,4CAA+G;AAE/G,oEAAuF;AACvF,mCAA2G;AAC3G,qCAA8C;AAC9C,+EAAqD;AA6FrD,MAAa,eAMX,SAAQ,0BAAY;IACpB,MAAM,CAAC,cAAc,CAAC,OAAgB,EAAE,IAAkB;QACxD,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,KAAK,WAA4B,GAAG,IAAoB;YAC7D,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEtC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAA;QAC9G,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAAgB,EAAE,IAAkB;QAC9D,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,KAAK,WAAqC,GAAG,IAAoB;YACtE,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAA;QAC1H,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,EAAiB,EAAE,IAAkB;QAC/E,MAAM,MAAM,GAAG,IAAA,mCAAuB,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAEnD,OAAO,KAAK,WAAqC,GAAG,IAAoB;YACtE,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;YACvB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEjC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAA;QAAI,CAAC,CAAC;IAC7H,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,MAAmB,EAAE,IAAkB;QACjE,MAAM,MAAM,GAAG,IAAA,iCAAqB,EAAC,MAAM,CAAC,CAAC;QAC7C,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEvD,OAAO,KAAK,WAA4B,GAAG,IAAoB;YAC7D,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC5B,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAErC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAA;QAC5G,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,iBAAiB,GAAG,IAAI,4BAAgB,EAAY,CAAC;IAE5D,MAAM,CAAC,MAAM,CAOX,aAAwF,EACxF,OAAmC;QAGnC,IAAI,IAAI,GAAG,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAChE,IAAG,CAAC,IAAI,EAAE,CAAC;YACT,IAAI,GAAG,IAAA,wBAAY,EAAC;gBAClB,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,kBAAQ;gBAClB,aAAa,EAAE,eAAe,CAAC,cAAc;gBAC7C,mBAAmB,EAAE,eAAe,CAAC,oBAAoB;gBACzD,qBAAqB,EAAE,eAAe,CAAC,sBAAsB;gBAC7D,mBAAmB,EAAE,eAAe,CAAC,oBAAoB;gBACzD,MAAM,EAAE,aAAa;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACrE,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC7D,CAAC;QAED,gFAAgF;QAChF,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,IAAI,CACN,aAAa,EACb,OAAO,CACR,CACkD,CAAC;IACxD,CAAC;IAED,iBAAiB;IACjB,MAAM,CAAC,SAAS,GAAG;QACjB,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,cAAc,EAAE,IAAI;QACpB,YAAY,EAAE,IAAI;KACQ,CAAC;IAEpB,cAAc,CAAqD;IACnE,QAAQ,CAAmB;IAE3B,YAAY,GAAG,IAAI,8BAAgB,EAAgD,CAAC;IAE7F;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACxC,CAAC;IAEQ,aAAa,GAAG,IAAI,8BAAgB,EAAgD,CAAC;IAE9F;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;IAC1E,CAAC;IAEQ,WAAW,GAAG,IAAI,8BAAgB,EAKvC,CAAC;IAEL;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,OAAO,GAAG,KAAK,CAAC;IAEhB;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,UAAU,GAAG,KAAK,CAAC;IAEnB;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,gBAAgB,CAAiC;IACjD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,YACE,aAA+D,EAC/D,OAAmC;QAEnC,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,QAAQ,GAAG;YACd,GAAG,eAAe,CAAC,SAAS;YAC5B,GAAG,OAAO;SACX,CAAC;QACF,IAAI,OAAO,EAAE,eAAe,EAAE,CAAC;YAC7B,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAChC,aAAa,GAAG,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,YAAY,qCAA6B,EAAE,CAAC;gBACrE,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC;gBAC1C,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,eAAe,GAAG,IAAI,kCAA0B,CAAC,SAAS,CAAC,CAAC;gBAC1G,iHAAiH;YAC3G,CAAC;QACH,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,UAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAuD,CAAC;IAChJ,CAAC;IAEO,KAAK,GAAG,IAAI,CAAC;IACb,eAAe,CAAgC;IAEvD,kBAAkB,CAGhB,OAAgB;QAChB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC;QAChC,OAAO,KAMN,CAAC;IACJ,CAAC;IAED,oBAAoB,CAIlB,GAAM,EACN,KAAQ;QAER,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAC;QACpE,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnC,OAAO,KAMN,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe,CAAmC,WAAyB;QACzE,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAwB;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;YAAE,OAAO,CAAC,qBAAqB;QACrD,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAE1B,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,OAAO,IAAmE,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CACxC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;aACxB,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CACxD,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;YAC1B,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,CAAI,EAA4C;QACrD,OAAO,IAAI,OAAO,CAAa,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,EAC5C,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YACpC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,OAAO,CAAC;gBACZ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;oBAC3C,OAAO,GAAG,UAAU,CAClB,GAAG,EAAE;wBACH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;wBAC1C,MAAM,CAAC,IAAI,qBAAY,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,gBAAgB;oBAC5E,CAAC,EACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CACnC,CAAC;gBACJ,CAAC;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;oBACvC,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,MAAM;oBACN,EAAE;iBACH,CAAC,CAAC;gBAEH,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACpD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACvB,CAAC;gBAED,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnD,aAAa;YACb,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CACV,IAAoE,EACpE,OAA+C,EAC/C,MAAkC,EAClC,EAAyC;QAEzC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;YAC9B,MAAM;iBACL,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;iBACrB,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,CAAC;YAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,aAAa,CAAC,IAAoE;QAChF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED,cAAc,CAAkB;IAEhC,gBAAgB;QACd,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEvD,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACtF,CAAC;IAED,QAAQ;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAChG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,qBAAqB;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAG,CAAA;YACzC,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,WAAW,CACT,IAA0B,EAC1B,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,KAAK;QAEH,OAAO,IAAM,IAAY,CAAC,KAAe,CACvC,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAC5F,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACrE,IAAI,CAAC,eAAe,EAAE,WAAW,CAClC,CAAC;IACJ,CAAC;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IAEnB,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU;YAAE,OAAO,CAAC,mBAAmB;QACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;YAAE,OAAO,CAAC,mBAAmB;QAEpD,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBAC7C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YAChC,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBAC9C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YAChC,CAAC;YAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5B,IAAI,CAAC,gBAAgB,EAAE,WAAW,EAAE,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;QAEf,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QAChC,CAAC;IACH,CAAC;IAED,OAAO;QACL,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAEhC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,WAAW,EAAE,CAAC;QAE3C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;IAC7B,CAAC;;AAxbH,0CAybC"}