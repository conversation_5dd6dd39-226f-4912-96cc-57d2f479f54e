import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '@redis/client/dist/lib/RESP/types';
import { RedisVariadicArgument } from '@redis/client/dist/lib/commands/generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Adds one or more items to a Top-K filter and returns items dropped from the top-K list
     * @param parser - The command parser
     * @param key - The name of the Top-K filter
     * @param items - One or more items to add to the filter
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, items: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=ADD.d.ts.map