import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Estimates the cumulative distribution function for values in a t-digest sketch
     * @param parser - The command parser
     * @param key - The name of the t-digest sketch
     * @param values - Array of values to get CDF estimates for
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, values: Array<number>) => void;
    readonly transformReply: {
        2: (reply: import("@redis/client/dist/lib/RESP/types").BlobStringReply<string>[], preserve?: any, typeMapping?: import("@redis/client/dist/lib/RESP/types").TypeMapping | undefined) => import("@redis/client/dist/lib/RESP/types").DoubleReply<number>[];
        3: () => import("@redis/client/dist/lib/RESP/types").ArrayReply<import("@redis/client/dist/lib/RESP/types").DoubleReply<number>>;
    };
};
export default _default;
//# sourceMappingURL=CDF.d.ts.map