/* eslint-disable camelcase */
/*
exports.shorthands = undefined;

exports.up = pgm => {};

exports.down = pgm => {};
*/

exports.up = pgm => {
  pgm.createTable('songs', {
    id: { type: 'VARCHAR(50)', primaryKey: true },
    title: { type: 'TEXT', notNull: true },
    year: { type: 'INTEGER', notNull: true },
    performer: { type: 'TEXT', notNull: true },
    genre: { type: 'TEXT', notNull: true },
    duration: { type: 'INTEGER' },
    albumId: {
      type: 'VARCHAR(50)',
      references: 'albums',
      onDelete: 'CASCADE',
    },
  });
};

exports.down = pgmn => {
  pgm.dropTable('songs');
};
