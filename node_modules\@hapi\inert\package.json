{"name": "@hapi/inert", "description": "Static file and directory handlers plugin for hapi.js", "version": "7.1.0", "repository": "https://github.com/hapijs/inert.git", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["file", "directory", "handler", "hapi", "plugin"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/ammo": "^6.0.1", "@hapi/boom": "^10.0.1", "@hapi/bounce": "^3.0.1", "@hapi/hoek": "^11.0.2", "@hapi/validate": "^2.0.1", "lru-cache": "^7.14.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/file": "^3.0.0", "@hapi/hapi": "^21.3.0", "@hapi/lab": "^25.1.2", "@types/node": "^14.18.37", "joi": "^17.8.3", "typescript": "^4.9.5"}, "scripts": {"test": "lab -f -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -f -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}