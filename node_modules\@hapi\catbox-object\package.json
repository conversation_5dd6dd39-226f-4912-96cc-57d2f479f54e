{"name": "@hapi/catbox-object", "description": "Memory object adapter for catbox", "version": "3.0.1", "repository": "git://github.com/hapijs/catbox-object", "main": "lib/index.js", "files": ["lib"], "keywords": ["cache", "catbox", "memory", "object"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/catbox": "^12.1.1", "@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2"}, "scripts": {"test": "lab -a @hapi/code -L -t 100", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}