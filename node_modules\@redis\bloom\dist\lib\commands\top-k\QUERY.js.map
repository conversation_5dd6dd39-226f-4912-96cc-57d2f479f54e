{"version": 3, "file": "QUERY.js", "sourceRoot": "", "sources": ["../../../../lib/commands/top-k/QUERY.ts"], "names": [], "mappings": ";;AAEA,+FAAyH;AAEzH,kBAAe;IACb,YAAY,EAAE,KAAK;IACnB;;;;;OAKG;IACH,YAAY,CAAC,MAAqB,EAAE,GAAkB,EAAE,KAA4B;QAClF,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpB,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IACD,cAAc,EAAE,iDAA0B;CAChB,CAAC"}