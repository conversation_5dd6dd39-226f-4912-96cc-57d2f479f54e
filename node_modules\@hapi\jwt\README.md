<a href="http://hapijs.com"><img src="https://raw.githubusercontent.com/hapijs/assets/master/images/family.png" width="180px" align="right" /></a>

# @hapi/jwt

#### JWT (JSON Web Token) Authentication.
**jwt** is part of the **hapi** ecosystem and was designed to work seamlessly with the [hapi web framework](https://hapi.dev) and its other components (but works great on its own or with other frameworks). If you are using a different web framework and find this module useful, check out [hapi](https://hapi.dev) – they work even better together.

### Visit the [hapi.dev](https://hapi.dev) Developer Portal for tutorials, documentation, and support

## Useful resources

- [Documentation and API](https://hapi.dev/family/jwt/)
- [Versions status](https://hapi.dev/resources/status/#jwt) (builds, dependencies, node versions, licenses, eol)
- [Changelog](https://hapi.dev/family/jwt/changelog/)
- [Project policies](https://hapi.dev/policies/)

## Acknowledgements

Portions of this module were adapted from:

- [node-jwa](https://github.com/brianloveswords/node-jwa), copyright (c) 2013 Brian J. Brennan, MIT License
- [node-jws](https://github.com/brianloveswords/node-jws), copyright (c) 2013 Brian J. Brennan, MIT License
- [jsonwebtoken](https://github.com/auth0/node-jsonwebtoken), copyright (c) 2015 Auth0, Inc., MIT License
- [node-jwks-rsa](https://github.com/auth0/node-jwks-rsa), copyright (c) 2016 Sandrino Di Mattia, MIT License
- [hapi-auth-jwt2](https://github.com/dwyl/hapi-auth-jwt2), copyright (c) 2015-2016, dwyl ltd, ISC License
- [mock-jwks](https://github.com/Levino/mock-jwks), copyright (c) 2018-2019 Levin Keller, MIT License
- [Stack Overflow answer](http://stackoverflow.com/questions/18835132/xml-to-pem-in-node-js)
- [node-rsa-pem-from-mod-exp](https://github.com/tracker1/node-rsa-pem-from-mod-exp), copyright (c) 2014 Michael J. Ryan, MIT License
